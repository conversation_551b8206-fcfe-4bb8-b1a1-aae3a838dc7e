# Feasibility Analysis: Moving Braze Identify Resolution Flow to Batch Processing

## Executive Summary

This document analyzes the feasibility of moving Braze's identify resolution flow (merging alias with users) for identify calls from the current direct API call approach to the batch processing flow. The analysis covers technical constraints, API limitations, response handling capabilities, and implementation challenges.

## Current Implementation Overview

### Current Identify Resolution Flow

The current implementation uses a **non-batched, direct API call** mechanism:

1. **Entry Point**: `processIdentify()` function in `src/v0/destinations/braze/transform.js`
2. **Endpoint**: `/users/identify`
3. **Trigger Condition**: When both `anonymousId` (or custom alias) and `brazeExternalID` are present
4. **Process**: Direct HTTP call using `handleHttpRequest()` during event processing
5. **Multiplexing**: Creates an intermediate API call but not true multiplexing

```javascript
// Current trigger logic
const brazeExternalID = getDestinationExternalID(message, 'brazeExternalId') || message.userId;
if ((message.anonymousId || isAliasPresent) && brazeExternalID) {
  await processIdentify({ message, destination });
}
```

### Current Batch Flow Architecture

1. **Entry Point**: `processRouterDest()` function
2. **Process**: Groups events by userId/anonymousId → processes each group → calls `processBatch()`
3. **Batching**: Separates events into arrays (attributes, events, purchases, subscriptions, merge_updates)
4. **Endpoints**: Uses `/users/track` for main batches, separate endpoints for subscriptions and merges
5. **Response Handling**: Limited rich response processing, metadata association challenges

## API Analysis

### /users/identify Endpoint Capabilities

#### Batching Support
- ✅ **Supported**: Yes, up to **50 aliases per request**
- ✅ **Request Format**: `aliases_to_identify` array with multiple alias objects
- ✅ **Merge Behavior**: Supports `merge` and `none` options

#### Rich Response Support
- ✅ **Partial Failure Handling**: Yes, returns index-based error reporting
- ✅ **Response Format**:
  ```json
  {
    "aliases_processed": 1,
    "message": "success",
    "errors": [
      {
        "type": "error_message",
        "input_array": "aliases_to_identify",
        "index": 0
      }
    ]
  }
  ```

#### Error Categories

**Validation Errors (Captured by Destination)**:
- Missing required fields (external_id, alias_label, alias_name)
- Invalid data types (all fields must be strings)
- Returns: Success response with errors array including index

**Silent Failures (NOT Captured by Destination)**:
- User with external_id doesn't exist on Braze
- User with alias doesn't exist on Braze
- Returns: Success response with NO errors

#### Rate Limits
- **Shared Rate Limit**: 20,000 requests per minute
- **Shared With**: `/users/delete`, `/users/alias/new`, `/users/merge`, `/users/alias/update`

## Current Batch Processing Analysis

### Metadata Association Challenges

**Current Issue**: In `processBatch()`, one-to-one association with job metadata is lost during batching.

```javascript
// Current processBatch logic loses individual event metadata
for (const transformedEvent of transformedEvents) {
  if (transformedEvent?.batchedRequest?.body?.JSON) {
    const { attributes, events, purchases, subscription_groups, merge_updates } =
      transformedEvent.batchedRequest.body.JSON;

    // Individual event metadata is aggregated into successMetadata array
    successMetadata.push(transformedEvent.metadata);
  }
}
```

**Impact**: Cannot map individual alias resolution failures back to specific events for targeted retry.

### Network Handler Response Processing

**Current Capabilities**:
- ✅ Detects partial failures (`response.errors.length > 0`)
- ✅ Handles application-level errors
- ❌ **Missing**: Index-based error mapping to individual events
- ❌ **Missing**: Granular retry logic for specific failed aliases

```javascript
// Current network handler - limited rich response usage
if (response.message === 'success' && response.errors && response.errors.length > 0) {
  stats.increment('braze_partial_failure');
  // No individual event failure mapping
}
```

## Feasibility Assessment

### ✅ Technical Feasibility: HIGH

1. **API Support**: `/users/identify` endpoint fully supports batching (50 aliases/request)
2. **Rich Responses**: Endpoint provides index-based error reporting
3. **Integration Pattern**: Similar to existing merge_updates batching in `prepareGroupAndAliasBatch()`

### ⚠️ Implementation Complexity: MEDIUM-HIGH

#### Required Changes

1. **Batch Processing Enhancement**
   - Extend `processBatch()` to handle `aliases_to_identify` arrays
   - Implement chunking logic (max 50 aliases per request)
   - Add alias batching to `prepareGroupAndAliasBatch()` pattern

2. **Metadata Preservation**
   - Maintain event-to-alias mapping during batching
   - Preserve individual job metadata for each alias operation
   - Enable index-based error mapping back to original events

3. **Network Handler Enhancement**
   - Implement rich response processing for `/users/identify`
   - Index based processing ( Not sure )

4. **Flow Modification**
   - Remove direct `processIdentify()` calls during event processing
   - Collect alias operations during transformation phase
   - Defer alias resolution to batch processing phase

### ❌ Current Limitations

1. **Multiplexing Complexity**: Identify events currently trigger both alias resolution AND attribute updates
   - Alias resolution would be batched
   - Attribute updates would remain in separate `/users/track` calls
   - **Challenge**: Coordinating success/failure across multiple API calls for single event

2. **Error Handling Granularity**:
   - Cannot fail entire identify event if only alias resolution fails
   - Attribute updates might succeed while alias resolution fails
   - **Challenge**: Partial event success scenarios

3. **Silent Failures**:
   - Braze doesn't report when users don't exist
   - No way to detect or retry these scenarios
   - **Impact**: Same limitation exists in current implementation

## Recommended Implementation Approach

1. Implement alias batching in `processBatch()`
2. Add chunking logic for 50 aliases per request
3. Extend network handler for basic rich response processing
4. Implement behind feature flag for testing

## Implementation Considerations

### Current Code Patterns to Leverage

1. **Existing Merge Batching**: The `prepareGroupAndAliasBatch()` function already handles merge_updates batching
2. **Chunking Logic**: Already implemented for attributes/events/purchases (75 items per chunk)
3. **Event Data Aggregation**: Pattern exists in `processBatch()` for collecting event data arrays

### Code Changes Required

#### 1. Transform Phase Modifications
```javascript
// In process() function - collect alias operations instead of direct calls
case EventType.IDENTIFY: {
  const brazeExternalID = getDestinationExternalID(message, 'brazeExternalId') || message.userId;
  if ((message.anonymousId || isAliasPresent) && brazeExternalID) {
    // Instead of: await processIdentify({ message, destination });
    // Collect for batching:
    const aliasPayload = getIdentifyPayload(message);
    return buildBatchedResponse(message, destination, { aliases_to_identify: aliasPayload });
  }
}
```

#### 2. Batch Processing Enhancements
```javascript
// In processBatch() - handle aliases_to_identify arrays
const aliasesArray = [];
for (const transformedEvent of transformedEvents) {
  if (transformedEvent?.batchedRequest?.body?.JSON?.aliases_to_identify) {
    aliasesArray.push(...transformedEvent.batchedRequest.body.JSON.aliases_to_identify);
    // Preserve metadata mapping for each alias
  }
}
```

#### 3. Network Handler Enhancements
```javascript
// Enhanced response handler for /users/identify
if (endpoint.includes('/users/identify') && response.errors) {
  // Map errors back to individual events using index
  response.errors.forEach(error => {
    // Fail the entire batch?
  });
}
```

## Conclusion

**Feasibility**: ✅ **TECHNICALLY FEASIBLE (WITHOUT RICH ERROR HANDLING)** with medium-high implementation complexity
